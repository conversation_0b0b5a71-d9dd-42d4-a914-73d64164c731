# Design Document

## Overview

The FlowNode control type extends the property controls system with flow management capabilities. It combines the simplicity of enum controls (predefined options) with the flow creation/management logic from NodeBindingActionControl.

## Architecture

### Control Type System Integration
- Add `FlowNode = 'flowNode'` to ControlType enum
- Follow existing property control handler pattern
- Register handler in imports.tsx

### Flow Management Integration
- Reuse existing flow management hooks and context
- Store flow IDs using `{property}_nodeBindingActionFlowId` pattern
- Create flows with trigger + action node structure

## Components and Interfaces

### PropertyControl Interface Extension
```typescript
// No changes needed - existing interface supports options array
{
  type: ControlType.FlowNode,
  title: string,
  defaultValue?: string,
  options: {label: string, value: string}[]
}
```

### FlowNode Control Component
- Similar structure to EnumPropertyControl
- Additional flow management logic from NodeBindingActionControl
- Dropdown for action selection + flow settings display

### Flow Management Functions
- `handleActionSelect()` - Create/update flows when action changes
- `updateNodePayload()` - Update flow node settings
- `switchToLogicTab()` - Navigate to flow editor

## Data Models

### Flow Structure
```typescript
// Reuse existing flow types from NodeBindingActionControl
- CampaignFlow with trigger + action nodes
- Flow ID: 'widget_' + widgetId
- Trigger node: OnButtonPress type
- Action node: Selected from options
```

### Settings Storage
```typescript
// Widget settings store flow ID
{
  [propertyName + '_nodeBindingActionFlowId']: string
}
```

## Error Handling

- Handle missing flow management hooks gracefully
- Validate action options exist before rendering
- Default to empty state if flow creation fails

## Testing Strategy

- Unit tests for FlowNode control component
- Integration tests with flow management system
- Test flow creation/update/deletion scenarios
- Verify settings storage and retrieval