import { ControlType, getPropertyControlsNew } from "@repo/shared/components/editor/property-controls-new";
import { addPropertyControlHandler } from "../../property-controls/propertyControlHandler";
import { Widget } from "@repo/shared/lib/types/editor";
import { useWidgetSettings } from "@/lib/hooks/useWidgetSettings";
import { Label } from "@repo/shared/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/shared/components/ui/select";
import { CampaignFlow, CampaignFlowNodeType, createNode, OnButtonPressPayload } from '@repo/shared/lib/campaign/actionNode';
import { FlowEditorContext, NodeSettings, nodeTypeMetadata } from '@/components/editor/flow/flowEditor';
import { useCampaignEditor } from '@/lib/hooks/useCampaignEditor';
import { useEventEmitter } from '@/lib/hooks/useEditorEventBus';
import { ZapIcon, ChevronRight } from 'lucide-react';

function FlowNodePropertyControl(props: {widget: Widget, property: string}) {
    const propertyName = props.property + '_nodeBindingActionFlowId';
    const { settings, updateSettings } = useWidgetSettings(props.widget);
    const propertyControls = getPropertyControlsNew(props.widget.componentName);
    const propertyControl = propertyControls?.[props.property];
    
    const flowId = settings?.[propertyName];
    const { flows, setFlows } = useCampaignEditor();
    const flow = flows?.find((f) => f.id == flowId);
    const emitter = useEventEmitter();

    const options = propertyControl?.options || [];

    const handleActionSelect = (value: string) => {
        // Create a new node of the selected type
        if (value) {
            const newFlowId = 'widget_' + props.widget.id;
            const triggerNode = createNode(CampaignFlowNodeType.OnButtonPress, props.widget.id + '_trigger', { x: 0, y: 0 }, {
                buttonId: props.widget.id,
            } as OnButtonPressPayload);

            const newActionNode = createNode(value as CampaignFlowNodeType, props.widget.id + '_action', { x: 0, y: 260 }, {});

            const newFlow: CampaignFlow = {
                name: 'Button ' + props.widget.id,
                id: newFlowId,
                nodes: [triggerNode, newActionNode],
            };

            triggerNode.connections.push(newActionNode.id);

            updateSettings({
                [propertyName]: newFlow?.id,
            });
            
            setFlows((draft) => {
                const existingFlowIndex = draft.findIndex((f) => f.id === newFlowId);
                if (existingFlowIndex !== -1) {
                    draft[existingFlowIndex] = { ...newFlow };
                } else {
                    draft.push(newFlow);
                }
            });

            emitter.emit('editor:flow_changed', {});
        }
    };

    const updateNodePayload = (nodeId: string, payload: any) => {
        setFlows((draft) => {
            const flowIndex = draft.findIndex((f) => f.id === flowId);
            if (flowIndex !== -1) {
                const updatedNodes = draft[flowIndex].nodes.map((node) => {
                    if (node.id === nodeId) {
                        return {
                            ...node,
                            payload,
                        };
                    }
                    return node;
                });
                draft[flowIndex] = {
                    ...draft[flowIndex],
                    nodes: updatedNodes,
                };
            }
        });
        emitter.emit('editor:flow_changed', {});
    };

    function switchToLogicTab(): void {
        emitter.emit('editor:show_flow', {
            flowId: flowId,
        });
    }

    return (
        <FlowEditorContext.Provider value={{ updateNodePayload, removeNodeById: null, handleAddNode: null }}>
            <div className="space-y-6 border-muted border-2 p-3 rounded-sm">
                <div className="space-y-4">
                    <Label>
                        <div className="flex gap-2 items-center mb-4">
                            <ZapIcon width={16} height={16}></ZapIcon>
                            {propertyControl?.title ?? props.property}
                        </div>
                    </Label>
                    <Select onValueChange={handleActionSelect} value={Object.values(CampaignFlowNodeType).find((k) => k == flow?.nodes?.[1]?.type)}>
                        <SelectTrigger>
                            <SelectValue placeholder="Choose an action..." />
                        </SelectTrigger>
                        <SelectContent>
                            {options.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                    {nodeTypeMetadata[option.value as CampaignFlowNodeType]?.title || option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    <div className="mt-4">
                        <NodeSettings campaignNode={flow?.nodes?.[1]} />
                    </div>
                </div>

                <div className="flex items-center gap-4 p-3 rounded-md cursor-pointer hover:bg-accent transition-colors" onClick={() => switchToLogicTab()}>
                    <ChevronRight className="w-4 h-4" />
                    <div className="flex flex-col">
                        <span className="text-xs font-medium">View in Logic Editor</span>
                        {flow?.nodes && Object.keys(flow.nodes).length - 2 > 0 && (
                            <span className="text-xs text-muted-foreground mt-1">Flow contains {Object.keys(flow.nodes).length - 2} additional nodes not displayed here</span>
                        )}
                    </div>
                </div>
            </div>
        </FlowEditorContext.Provider>
    );
}

addPropertyControlHandler(ControlType.FlowNode, (props: {widget: Widget, property: string}) => <FlowNodePropertyControl {...props}/>);