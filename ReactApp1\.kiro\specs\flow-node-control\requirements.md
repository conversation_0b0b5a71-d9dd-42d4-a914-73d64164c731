# Requirements Document

## Introduction

Add a new FlowNode control type that works like NodeBindingActionControl but with predefined action options (like enum controls).

## Requirements

### Requirement 1

**User Story:** As a developer, I want a FlowNode control type, so that I can configure flow actions with predefined options.

#### Acceptance Criteria

1. <PERSON><PERSON><PERSON> defining FlowNode control THEN system SHALL add ControlType.FlowNode enum
2. WHEN using FlowNode control THEN system SHALL accept options array like enum controls
3. WHEN user selects action THEN system SHALL create/update flows like NodeBindingActionControl

### Requirement 2

**User Story:** As a developer, I want FlowNode to manage flows properly, so that flows are created/deleted correctly.

#### Acceptance Criteria

1. WHEN action selected THEN system SHALL create flow with trigger + action nodes
2. WHEN storing flow THEN system SHALL save flow ID as `{property}_nodeBindingActionFlowId`
3. WHEN action changed THEN system SHALL update existing flow or create new one