import {
  WidgetRenderingContextNew
} from "@repo/shared/lib/types/editor";
import { addWidgetMetadata } from "@repo/shared/lib/widget-metadata";
import { motion } from "framer-motion";
import { useFramerEffects } from "@repo/shared/lib/hooks/useFramerEffects";
import { addPropertyControlsNew, ControlType } from "../../editor/property-controls-new";
import { useExecutionStore } from "@repo/shared/lib/stores/useExecutionStore";
import { Loader2 } from "lucide-react";
import { CampaignFlowNodeType } from "@repo/shared/lib/campaign/actionNode";


export const ButtonWidgetNew: React.FC<
  WidgetRenderingContextNew<any>
> = ({
  widgetId,
  settings,
  editorContext,
  sceneId,
}) => {
    const isEditorMode = editorContext?.isEditorMode ?? false;
    const isSelectedInEditor = editorContext?.isSelectedInEditor ?? false;

    const { motionSettings } = useFramerEffects({
      effects: settings.effects,
      defaultVariant: { scale: 1, opacity: 1 },
    });


    const handleClick = () => {
      if (typeof window !== "undefined" && "gameEvents" in window) {
        (window as any).gameEvents.emit("ButtonPressed", {
          sceneId,
          buttonId: widgetId,
        });
      }
    };

    const buttonStyle = {
      backgroundColor: settings.backgroundColor,
      padding: settings.padding,
      margin: settings.margin,
      borderStyle: settings.border?.style || "none",
      borderWidth: settings.border?.width || "0px",
      borderColor: settings.border?.color || "transparent",
      borderRadius: settings.border?.radius || "0px",
      cursor: "pointer",
      display: "inline-block",
    }

    const textStyle = {
      width: "100%",
      whiteSpace: "pre-wrap" as const,
      fontSize: settings.fontSize,
      fontWeight: settings.fontWeight,
      fontFamily: settings.fontFamily,
      lineHeight: settings.lineHeight,
      letterSpacing: `${settings.letterSpacing}px`,
      textAlign: settings.textAlign,
      textTransform: settings.textTransform,
      fontStyle: settings.fontStyle,
      textDecoration: settings.textDecoration,
      color: settings.textColor,
    } as React.CSSProperties

    // const isDisabledAndLoading = settings.showLoader && isExecutingNodes;

    return (
      <motion.button
        {...motionSettings}
        onClick={handleClick}
        style={buttonStyle}
        // disabled={isDisabledAndLoading}
        className="disabled:opacity-50"
      >
        <div
          style={textStyle}
          className={`flex gap-2 items-center plausible-event-name=ButtonClicked plausible-event-widgetId=${widgetId} plausible-event-sceneId=${sceneId}`}
        >
          {/* {isDisabledAndLoading && <Loader2 className="animate-spin h-4 w-4" />} */}
          {settings.text}
        </div>
      </motion.button>
    );
  };

addWidgetMetadata(ButtonWidgetNew, {
  componentName: "ButtonWidgetNew",
  displayName: "Button",
  type: "block",
});

addPropertyControlsNew(ButtonWidgetNew, {
  // Text content
  text: {
    type: ControlType.Text,
    title: "Text",
    defaultValue: "Click Me"
  },

  // Typography
  fontSize: {
    type: ControlType.Text,
    title: "Font Size",
    defaultValue: "1.25rem"
  },
  fontWeight: {
    type: ControlType.Text,
    title: "Font Weight",
    defaultValue: "400"
  },
  fontFamily: {
    type: ControlType.Text,
    title: "Font Family",
    defaultValue: "Poppins"
  },
  textColor: {
    type: ControlType.Color,
    title: "Text Color",
    defaultValue: "#ffffff"
  },
  textAlign: {
    type: ControlType.Enum,
    title: "Text Align",
    defaultValue: "center",
    options: [
      { label: "Left", value: "left" },
      { label: "Center", value: "center" },
      { label: "Right", value: "right" }
    ]
  },
  textTransform: {
    type: ControlType.Enum,
    title: "Text Transform",
    defaultValue: "none",
    options: [
      { label: "None", value: "none" },
      { label: "Uppercase", value: "uppercase" },
      { label: "Lowercase", value: "lowercase" },
      { label: "Capitalize", value: "capitalize" }
    ]
  },
  lineHeight: {
    type: ControlType.Text,
    title: "Line Height",
    defaultValue: "1.2"
  },
  letterSpacing: {
    type: ControlType.Number,
    title: "Letter Spacing (px)",
    defaultValue: 0
  },
  fontStyle: {
    type: ControlType.Enum,
    title: "Font Style",
    defaultValue: "normal",
    options: [
      { label: "Normal", value: "normal" },
      { label: "Italic", value: "italic" }
    ]
  },
  textDecoration: {
    type: ControlType.Enum,
    title: "Text Decoration",
    defaultValue: "none",
    options: [
      { label: "None", value: "none" },
      { label: "Underline", value: "underline" },
      { label: "Line Through", value: "line-through" }
    ]
  },

  // Background
  backgroundColor: {
    type: ControlType.Color,
    title: "Background Color",
    defaultValue: "#3498db"
  },

  // Layout
  padding: {
    type: ControlType.Padding,
    title: "Padding",
    defaultValue: "12px 28px"
  },
  margin: {
    type: ControlType.Margin,
    title: "Margin",
    defaultValue: "0px"
  },

  // Border
  border: {
    type: ControlType.Border,
    title: "Border",
    defaultValue: {
      width: "0px",
      style: "none",
      color: "#2980b9",
      radius: "8px"
    }
  },

  // Behavior
  showLoader: {
    type: ControlType.Boolean,
    title: "Show Loader",
    defaultValue: false
  },

  // Flow Actions
  onButtonPress: {
    type: ControlType.FlowNode,
    title: "On Button Press",
    options: Object.entries(CampaignFlowNodeType)
      .filter(([key, value]) => {
          return !value.startsWith('trigger:') && !value.startsWith('placeholder:')
      })
      .map(([key, value]) => {
          return { value: value, label: key }
      })
  }
})