# Implementation Plan

- [x] 1. Add FlowNode to ControlType enum





  - Add `FlowNode = 'flowNode'` to ControlType enum in property-controls-new.ts
  - _Requirements: 1.1_

- [x] 2. Create FlowNode property control component





  - Create flowNode.tsx file in property-controls directory
  - Implement FlowNodePropertyControl component with dropdown and flow management
  - Copy flow management logic from NodeBindingActionControl (handleActionSelect, updateNodePayload, switchToLogicTab)
  - Use Select component for action dropdown like enum control
  - Display NodeSettings component for selected flow
  - _Requirements: 1.2, 2.1, 2.2_

- [x] 3. Register FlowNode control handler





  - Add property control handler registration using addPropertyControlHandler
  - Import flowNode.tsx in imports.tsx file
  - _Requirements: 1.1_

- [ ] 4. Test FlowNode control integration





  - Add FlowNode control to ButtonWidgetNew component for testing
  - _Requirements: 1.3, 2.1, 2.2_