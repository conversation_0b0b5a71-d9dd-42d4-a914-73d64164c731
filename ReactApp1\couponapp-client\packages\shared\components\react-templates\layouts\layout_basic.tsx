import { WidgetRenderingContextNew } from "@repo/shared/lib/types/editor";
import {
  BackgroundImageSettings,
  WidgetSettings,
} from "@repo/shared/lib/types/widgetSettings";
import { addWidgetMetadata } from "@repo/shared/lib/widget-metadata";
import { addPropertyControlsNew, ControlType } from "../../editor/property-controls-new";

export interface BasicLayoutSettings
  extends WidgetSettings,
  BackgroundImageSettings {
  backgroundColor: string;
  backgroundSize: "cover" | "contain" | "auto";
  backgroundPosition: string;
  backgroundRepeat: "no-repeat" | "repeat" | "repeat-x" | "repeat-y";
}

export const BasicLayoutWidget: React.FC<
  WidgetRenderingContextNew<BasicLayoutSettings>
> = ({ ...props }) => {
  const { settings, children, resolveAssetUrl } = props;

  const layoutStyle: React.CSSProperties = {
    backgroundColor: settings.backgroundColor ?? "rgba(255, 255, 237, 0.07)",
    backgroundImage:
      settings.backgroundImage && resolveAssetUrl
        ? `url(${resolveAssetUrl(settings.backgroundImage)})`
        : "none",
    backgroundSize: settings.backgroundSize ?? "cover",
    backgroundPosition: settings.backgroundPosition ?? "center center",
    backgroundRepeat: settings.backgroundRepeat ?? "no-repeat",
    width: "100%",
    display: "flex",
    flexDirection: "column",
    flex: 1, // Add this
    alignSelf: "stretch", // And this if the parent is also a flex container
  };

  return (
    <div style={layoutStyle} className="basic-layout-widget">
      {children}
    </div>
  );
};

// Register basic layout widget metadata
addWidgetMetadata(BasicLayoutWidget, {
  componentName: "BasicLayoutWidget",
  type: "layout",
  description: "A basic layout container for organizing content",
  icon: "layout",
});

// Add property controls for the new system
addPropertyControlsNew(BasicLayoutWidget, {
  // Background Color
  backgroundColor: {
    type: ControlType.Color,
    title: "Background Color",
    defaultValue: "rgba(255, 255, 237, 0.07)"
  },

  // Background Image
  backgroundImage: {
    type: ControlType.Asset,
    title: "Background Image"
  },

  // Background Size
  backgroundSize: {
    type: ControlType.Enum,
    title: "Background Size",
    defaultValue: "cover",
    options: [
      { label: "Cover", value: "cover" },
      { label: "Contain", value: "contain" },
      { label: "Auto", value: "auto" }
    ]
  },

  // Background Position
  backgroundPosition: {
    type: ControlType.Text,
    title: "Background Position",
    defaultValue: "center center"
  },

  // Background Repeat
  backgroundRepeat: {
    type: ControlType.Enum,
    title: "Background Repeat",
    defaultValue: "no-repeat",
    options: [
      { label: "No Repeat", value: "no-repeat" },
      { label: "Repeat", value: "repeat" },
      { label: "Repeat X", value: "repeat-x" },
      { label: "Repeat Y", value: "repeat-y" }
    ]
  }
});
